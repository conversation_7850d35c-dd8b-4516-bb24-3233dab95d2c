import{g as Ta,r as f,R as Ca,j as p,a as ca,B as J,I as Pa}from"./index-ae0554d4.js";import{u as Ne}from"./documentStore-75b42437.js";var sa={exports:{}},Fa="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Aa=Fa,Ra=Aa;function da(){}function ma(){}ma.resetWarningCache=da;var Na=function(){function e(t,o,r,s,d,h){if(h!==Ra){var z=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw z.name="Invariant Violation",z}}e.isRequired=e;function a(){return e}var i={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:a,element:e,elementType:e,instanceOf:a,node:e,objectOf:a,oneOf:a,oneOfType:a,shape:a,exact:a,checkPropTypes:ma,resetWarningCache:da};return i.PropTypes=i,i};sa.exports=Na();var qa=sa.exports;const O=Ta(qa);function H(e,a,i,t){function o(r){return r instanceof i?r:new i(function(s){s(r)})}return new(i||(i=Promise))(function(r,s){function d(b){try{z(t.next(b))}catch(D){s(D)}}function h(b){try{z(t.throw(b))}catch(D){s(D)}}function z(b){b.done?r(b.value):o(b.value).then(d,h)}z((t=t.apply(e,a||[])).next())})}const Ma=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function Z(e,a,i){const t=La(e),{webkitRelativePath:o}=e,r=typeof a=="string"?a:typeof o=="string"&&o.length>0?o:`./${e.name}`;return typeof t.path!="string"&&Ke(t,"path",r),i!==void 0&&Object.defineProperty(t,"handle",{value:i,writable:!1,configurable:!1,enumerable:!0}),Ke(t,"relativePath",r),t}function La(e){const{name:a}=e;if(a&&a.lastIndexOf(".")!==-1&&!e.type){const t=a.split(".").pop().toLowerCase(),o=Ma.get(t);o&&Object.defineProperty(e,"type",{value:o,writable:!1,configurable:!1,enumerable:!0})}return e}function Ke(e,a,i){Object.defineProperty(e,a,{value:i,writable:!1,configurable:!1,enumerable:!0})}const $a=[".DS_Store","Thumbs.db"];function Wa(e){return H(this,void 0,void 0,function*(){return fe(e)&&Ua(e.dataTransfer)?Xa(e.dataTransfer,e.type):Ba(e)?Ha(e):Array.isArray(e)&&e.every(a=>"getFile"in a&&typeof a.getFile=="function")?Ka(e):[]})}function Ua(e){return fe(e)}function Ba(e){return fe(e)&&fe(e.target)}function fe(e){return typeof e=="object"&&e!==null}function Ha(e){return _e(e.target.files).map(a=>Z(a))}function Ka(e){return H(this,void 0,void 0,function*(){return(yield Promise.all(e.map(i=>i.getFile()))).map(i=>Z(i))})}function Xa(e,a){return H(this,void 0,void 0,function*(){if(e.items){const i=_e(e.items).filter(o=>o.kind==="file");if(a!=="drop")return i;const t=yield Promise.all(i.map(Va));return Xe(ua(t))}return Xe(_e(e.files).map(i=>Z(i)))})}function Xe(e){return e.filter(a=>$a.indexOf(a.name)===-1)}function _e(e){if(e===null)return[];const a=[];for(let i=0;i<e.length;i++){const t=e[i];a.push(t)}return a}function Va(e){if(typeof e.webkitGetAsEntry!="function")return Ve(e);const a=e.webkitGetAsEntry();return a&&a.isDirectory?fa(a):Ve(e,a)}function ua(e){return e.reduce((a,i)=>[...a,...Array.isArray(i)?ua(i):[i]],[])}function Ve(e,a){return H(this,void 0,void 0,function*(){var i;if(globalThis.isSecureContext&&typeof e.getAsFileSystemHandle=="function"){const r=yield e.getAsFileSystemHandle();if(r===null)throw new Error(`${e} is not a File`);if(r!==void 0){const s=yield r.getFile();return s.handle=r,Z(s)}}const t=e.getAsFile();if(!t)throw new Error(`${e} is not a File`);return Z(t,(i=a==null?void 0:a.fullPath)!==null&&i!==void 0?i:void 0)})}function Ya(e){return H(this,void 0,void 0,function*(){return e.isDirectory?fa(e):Ga(e)})}function fa(e){const a=e.createReader();return new Promise((i,t)=>{const o=[];function r(){a.readEntries(s=>H(this,void 0,void 0,function*(){if(s.length){const d=Promise.all(s.map(Ya));o.push(d),r()}else try{const d=yield Promise.all(o);i(d)}catch(d){t(d)}}),s=>{t(s)})}r()})}function Ga(e){return H(this,void 0,void 0,function*(){return new Promise((a,i)=>{e.file(t=>{const o=Z(t,e.fullPath);a(o)},t=>{i(t)})})})}var ze=function(e,a){if(e&&a){var i=Array.isArray(a)?a:a.split(",");if(i.length===0)return!0;var t=e.name||"",o=(e.type||"").toLowerCase(),r=o.replace(/\/.*$/,"");return i.some(function(s){var d=s.trim().toLowerCase();return d.charAt(0)==="."?t.toLowerCase().endsWith(d):d.endsWith("/*")?r===d.replace(/\/.*$/,""):o===d})}return!0};function Ye(e){return Za(e)||Ja(e)||xa(e)||Qa()}function Qa(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ja(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Za(e){if(Array.isArray(e))return Te(e)}function Ge(e,a){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),i.push.apply(i,t)}return i}function Qe(e){for(var a=1;a<arguments.length;a++){var i=arguments[a]!=null?arguments[a]:{};a%2?Ge(Object(i),!0).forEach(function(t){va(e,t,i[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):Ge(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}function va(e,a,i){return a in e?Object.defineProperty(e,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[a]=i,e}function oe(e,a){return ii(e)||ai(e,a)||xa(e,a)||ei()}function ei(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xa(e,a){if(e){if(typeof e=="string")return Te(e,a);var i=Object.prototype.toString.call(e).slice(8,-1);if(i==="Object"&&e.constructor&&(i=e.constructor.name),i==="Map"||i==="Set")return Array.from(e);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Te(e,a)}}function Te(e,a){(a==null||a>e.length)&&(a=e.length);for(var i=0,t=new Array(a);i<a;i++)t[i]=e[i];return t}function ai(e,a){var i=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(i!=null){var t=[],o=!0,r=!1,s,d;try{for(i=i.call(e);!(o=(s=i.next()).done)&&(t.push(s.value),!(a&&t.length===a));o=!0);}catch(h){r=!0,d=h}finally{try{!o&&i.return!=null&&i.return()}finally{if(r)throw d}}return t}}function ii(e){if(Array.isArray(e))return e}var ti=typeof ze=="function"?ze:ze.default,ni="file-invalid-type",oi="file-too-large",pi="file-too-small",ri="too-many-files",li=function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",i=a.split(","),t=i.length>1?"one of ".concat(i.join(", ")):i[0];return{code:ni,message:"File type must be ".concat(t)}},Je=function(a){return{code:oi,message:"File is larger than ".concat(a," ").concat(a===1?"byte":"bytes")}},Ze=function(a){return{code:pi,message:"File is smaller than ".concat(a," ").concat(a===1?"byte":"bytes")}},ci={code:ri,message:"Too many files"};function ga(e,a){var i=e.type==="application/x-moz-file"||ti(e,a);return[i,i?null:li(a)]}function ha(e,a,i){if(B(e.size))if(B(a)&&B(i)){if(e.size>i)return[!1,Je(i)];if(e.size<a)return[!1,Ze(a)]}else{if(B(a)&&e.size<a)return[!1,Ze(a)];if(B(i)&&e.size>i)return[!1,Je(i)]}return[!0,null]}function B(e){return e!=null}function si(e){var a=e.files,i=e.accept,t=e.minSize,o=e.maxSize,r=e.multiple,s=e.maxFiles,d=e.validator;return!r&&a.length>1||r&&s>=1&&a.length>s?!1:a.every(function(h){var z=ga(h,i),b=oe(z,1),D=b[0],k=ha(h,t,o),g=oe(k,1),w=g[0],n=d?d(h):null;return D&&w&&!n})}function ve(e){return typeof e.isPropagationStopped=="function"?e.isPropagationStopped():typeof e.cancelBubble<"u"?e.cancelBubble:!1}function ue(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(a){return a==="Files"||a==="application/x-moz-file"}):!!e.target&&!!e.target.files}function ea(e){e.preventDefault()}function di(e){return e.indexOf("MSIE")!==-1||e.indexOf("Trident/")!==-1}function mi(e){return e.indexOf("Edge/")!==-1}function ui(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return di(e)||mi(e)}function L(){for(var e=arguments.length,a=new Array(e),i=0;i<e;i++)a[i]=arguments[i];return function(t){for(var o=arguments.length,r=new Array(o>1?o-1:0),s=1;s<o;s++)r[s-1]=arguments[s];return a.some(function(d){return!ve(t)&&d&&d.apply(void 0,[t].concat(r)),ve(t)})}}function fi(){return"showOpenFilePicker"in window}function vi(e){if(B(e)){var a=Object.entries(e).filter(function(i){var t=oe(i,2),o=t[0],r=t[1],s=!0;return ba(o)||(console.warn('Skipped "'.concat(o,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),s=!1),(!Array.isArray(r)||!r.every(ya))&&(console.warn('Skipped "'.concat(o,'" because an invalid file extension was provided.')),s=!1),s}).reduce(function(i,t){var o=oe(t,2),r=o[0],s=o[1];return Qe(Qe({},i),{},va({},r,s))},{});return[{description:"Files",accept:a}]}return e}function xi(e){if(B(e))return Object.entries(e).reduce(function(a,i){var t=oe(i,2),o=t[0],r=t[1];return[].concat(Ye(a),[o],Ye(r))},[]).filter(function(a){return ba(a)||ya(a)}).join(",")}function gi(e){return e instanceof DOMException&&(e.name==="AbortError"||e.code===e.ABORT_ERR)}function hi(e){return e instanceof DOMException&&(e.name==="SecurityError"||e.code===e.SECURITY_ERR)}function ba(e){return e==="audio/*"||e==="video/*"||e==="image/*"||e==="text/*"||e==="application/*"||/\w+\/[-+.\w]+/g.test(e)}function ya(e){return/^.*\.[\w]+$/.test(e)}var bi=["children"],yi=["open"],wi=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],ki=["refKey","onChange","onClick"];function ji(e){return zi(e)||Si(e)||wa(e)||Di()}function Di(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Si(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function zi(e){if(Array.isArray(e))return Ce(e)}function Oe(e,a){return Ii(e)||Ei(e,a)||wa(e,a)||Oi()}function Oi(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wa(e,a){if(e){if(typeof e=="string")return Ce(e,a);var i=Object.prototype.toString.call(e).slice(8,-1);if(i==="Object"&&e.constructor&&(i=e.constructor.name),i==="Map"||i==="Set")return Array.from(e);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return Ce(e,a)}}function Ce(e,a){(a==null||a>e.length)&&(a=e.length);for(var i=0,t=new Array(a);i<a;i++)t[i]=e[i];return t}function Ei(e,a){var i=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(i!=null){var t=[],o=!0,r=!1,s,d;try{for(i=i.call(e);!(o=(s=i.next()).done)&&(t.push(s.value),!(a&&t.length===a));o=!0);}catch(h){r=!0,d=h}finally{try{!o&&i.return!=null&&i.return()}finally{if(r)throw d}}return t}}function Ii(e){if(Array.isArray(e))return e}function aa(e,a){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);a&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),i.push.apply(i,t)}return i}function I(e){for(var a=1;a<arguments.length;a++){var i=arguments[a]!=null?arguments[a]:{};a%2?aa(Object(i),!0).forEach(function(t){Pe(e,t,i[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):aa(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}function Pe(e,a,i){return a in e?Object.defineProperty(e,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[a]=i,e}function xe(e,a){if(e==null)return{};var i=_i(e,a),t,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(o=0;o<r.length;o++)t=r[o],!(a.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(i[t]=e[t])}return i}function _i(e,a){if(e==null)return{};var i={},t=Object.keys(e),o,r;for(r=0;r<t.length;r++)o=t[r],!(a.indexOf(o)>=0)&&(i[o]=e[o]);return i}var qe=f.forwardRef(function(e,a){var i=e.children,t=xe(e,bi),o=ja(t),r=o.open,s=xe(o,yi);return f.useImperativeHandle(a,function(){return{open:r}},[r]),Ca.createElement(f.Fragment,null,i(I(I({},s),{},{open:r})))});qe.displayName="Dropzone";var ka={disabled:!1,getFilesFromEvent:Wa,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};qe.defaultProps=ka;qe.propTypes={children:O.func,accept:O.objectOf(O.arrayOf(O.string)),multiple:O.bool,preventDropOnDocument:O.bool,noClick:O.bool,noKeyboard:O.bool,noDrag:O.bool,noDragEventsBubbling:O.bool,minSize:O.number,maxSize:O.number,maxFiles:O.number,disabled:O.bool,getFilesFromEvent:O.func,onFileDialogCancel:O.func,onFileDialogOpen:O.func,useFsAccessApi:O.bool,autoFocus:O.bool,onDragEnter:O.func,onDragLeave:O.func,onDragOver:O.func,onDrop:O.func,onDropAccepted:O.func,onDropRejected:O.func,onError:O.func,validator:O.func};var Fe={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ja(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},a=I(I({},ka),e),i=a.accept,t=a.disabled,o=a.getFilesFromEvent,r=a.maxSize,s=a.minSize,d=a.multiple,h=a.maxFiles,z=a.onDragEnter,b=a.onDragLeave,D=a.onDragOver,k=a.onDrop,g=a.onDropAccepted,w=a.onDropRejected,n=a.onFileDialogCancel,c=a.onFileDialogOpen,m=a.useFsAccessApi,x=a.autoFocus,y=a.preventDropOnDocument,E=a.noClick,v=a.noKeyboard,j=a.noDrag,S=a.noDragEventsBubbling,R=a.onError,P=a.validator,M=f.useMemo(function(){return xi(i)},[i]),$=f.useMemo(function(){return vi(i)},[i]),ee=f.useMemo(function(){return typeof c=="function"?c:ia},[c]),K=f.useMemo(function(){return typeof n=="function"?n:ia},[n]),C=f.useRef(null),A=f.useRef(null),ge=f.useReducer(Ti,Fe),ae=Oe(ge,2),W=ae[0],F=ae[1],pe=W.isFocused,re=W.isFileDialogActive,X=f.useRef(typeof window<"u"&&window.isSecureContext&&m&&fi()),ie=function(){!X.current&&re&&setTimeout(function(){if(A.current){var u=A.current.files;u.length||(F({type:"closeDialog"}),K())}},300)};f.useEffect(function(){return window.addEventListener("focus",ie,!1),function(){window.removeEventListener("focus",ie,!1)}},[A,re,K,X]);var q=f.useRef([]),te=function(u){C.current&&C.current.contains(u.target)||(u.preventDefault(),q.current=[])};f.useEffect(function(){return y&&(document.addEventListener("dragover",ea,!1),document.addEventListener("drop",te,!1)),function(){y&&(document.removeEventListener("dragover",ea),document.removeEventListener("drop",te))}},[C,y]),f.useEffect(function(){return!t&&x&&C.current&&C.current.focus(),function(){}},[C,x,t]);var U=f.useCallback(function(l){R?R(l):console.error(l)},[R]),Me=f.useCallback(function(l){l.preventDefault(),l.persist(),de(l),q.current=[].concat(ji(q.current),[l.target]),ue(l)&&Promise.resolve(o(l)).then(function(u){if(!(ve(l)&&!S)){var _=u.length,T=_>0&&si({files:u,accept:M,minSize:s,maxSize:r,multiple:d,maxFiles:h,validator:P}),N=_>0&&!T;F({isDragAccept:T,isDragReject:N,isDragActive:!0,type:"setDraggedFiles"}),z&&z(l)}}).catch(function(u){return U(u)})},[o,z,U,S,M,s,r,d,h,P]),Le=f.useCallback(function(l){l.preventDefault(),l.persist(),de(l);var u=ue(l);if(u&&l.dataTransfer)try{l.dataTransfer.dropEffect="copy"}catch{}return u&&D&&D(l),!1},[D,S]),$e=f.useCallback(function(l){l.preventDefault(),l.persist(),de(l);var u=q.current.filter(function(T){return C.current&&C.current.contains(T)}),_=u.indexOf(l.target);_!==-1&&u.splice(_,1),q.current=u,!(u.length>0)&&(F({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),ue(l)&&b&&b(l))},[C,b,S]),le=f.useCallback(function(l,u){var _=[],T=[];l.forEach(function(N){var ne=ga(N,M),G=Oe(ne,2),be=G[0],ye=G[1],we=ha(N,s,r),me=Oe(we,2),ke=me[0],je=me[1],De=P?P(N):null;if(be&&ke&&!De)_.push(N);else{var Se=[ye,je];De&&(Se=Se.concat(De)),T.push({file:N,errors:Se.filter(function(_a){return _a})})}}),(!d&&_.length>1||d&&h>=1&&_.length>h)&&(_.forEach(function(N){T.push({file:N,errors:[ci]})}),_.splice(0)),F({acceptedFiles:_,fileRejections:T,isDragReject:T.length>0,type:"setFiles"}),k&&k(_,T,u),T.length>0&&w&&w(T,u),_.length>0&&g&&g(_,u)},[F,d,M,s,r,h,k,g,w,P]),ce=f.useCallback(function(l){l.preventDefault(),l.persist(),de(l),q.current=[],ue(l)&&Promise.resolve(o(l)).then(function(u){ve(l)&&!S||le(u,l)}).catch(function(u){return U(u)}),F({type:"reset"})},[o,le,U,S]),V=f.useCallback(function(){if(X.current){F({type:"openDialog"}),ee();var l={multiple:d,types:$};window.showOpenFilePicker(l).then(function(u){return o(u)}).then(function(u){le(u,null),F({type:"closeDialog"})}).catch(function(u){gi(u)?(K(u),F({type:"closeDialog"})):hi(u)?(X.current=!1,A.current?(A.current.value=null,A.current.click()):U(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):U(u)});return}A.current&&(F({type:"openDialog"}),ee(),A.current.value=null,A.current.click())},[F,ee,K,m,le,U,$,d]),We=f.useCallback(function(l){!C.current||!C.current.isEqualNode(l.target)||(l.key===" "||l.key==="Enter"||l.keyCode===32||l.keyCode===13)&&(l.preventDefault(),V())},[C,V]),Ue=f.useCallback(function(){F({type:"focus"})},[]),Be=f.useCallback(function(){F({type:"blur"})},[]),He=f.useCallback(function(){E||(ui()?setTimeout(V,0):V())},[E,V]),Y=function(u){return t?null:u},he=function(u){return v?null:Y(u)},se=function(u){return j?null:Y(u)},de=function(u){S&&u.stopPropagation()},Oa=f.useMemo(function(){return function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=l.refKey,_=u===void 0?"ref":u,T=l.role,N=l.onKeyDown,ne=l.onFocus,G=l.onBlur,be=l.onClick,ye=l.onDragEnter,we=l.onDragOver,me=l.onDragLeave,ke=l.onDrop,je=xe(l,wi);return I(I(Pe({onKeyDown:he(L(N,We)),onFocus:he(L(ne,Ue)),onBlur:he(L(G,Be)),onClick:Y(L(be,He)),onDragEnter:se(L(ye,Me)),onDragOver:se(L(we,Le)),onDragLeave:se(L(me,$e)),onDrop:se(L(ke,ce)),role:typeof T=="string"&&T!==""?T:"presentation"},_,C),!t&&!v?{tabIndex:0}:{}),je)}},[C,We,Ue,Be,He,Me,Le,$e,ce,v,j,t]),Ea=f.useCallback(function(l){l.stopPropagation()},[]),Ia=f.useMemo(function(){return function(){var l=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},u=l.refKey,_=u===void 0?"ref":u,T=l.onChange,N=l.onClick,ne=xe(l,ki),G=Pe({accept:M,multiple:d,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Y(L(T,ce)),onClick:Y(L(N,Ea)),tabIndex:-1},_,A);return I(I({},G),ne)}},[A,i,d,ce,t]);return I(I({},W),{},{isFocused:pe&&!t,getRootProps:Oa,getInputProps:Ia,rootRef:C,inputRef:A,open:Y(V)})}function Ti(e,a){switch(a.type){case"focus":return I(I({},e),{},{isFocused:!0});case"blur":return I(I({},e),{},{isFocused:!1});case"openDialog":return I(I({},Fe),{},{isFileDialogActive:!0});case"closeDialog":return I(I({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return I(I({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return I(I({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return I({},Fe);default:return e}}function ia(){}const Ci=()=>{const[e,a]=f.useState(!1),[i,t]=f.useState([]),{uploadDocument:o,setUploadProgress:r}=Ne(),s=f.useCallback(async b=>{a(!0),t([]);const D=[];for(const k of b)try{if(k.size>10*1024*1024){D.push(`${k.name}: File size exceeds 10MB limit`);continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(k.type)){D.push(`${k.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);continue}r(k.name,0),await o(k),r(k.name,100)}catch(g){D.push(`${k.name}: ${g instanceof Error?g.message:"Unknown error"}`)}t(D),a(!1)},[o,r]),{getRootProps:d,getInputProps:h,isDragActive:z}=ja({onDrop:s,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,disabled:e});return p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{...d(),className:`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${z?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5"}
          ${e?"opacity-50 cursor-not-allowed":""}
        `,children:[p.jsx("input",{...h()}),p.jsxs("div",{className:"space-y-2",children:[p.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:p.jsx("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),z?p.jsx("p",{className:"text-primary-400",children:"Drop the files here..."}):p.jsxs("div",{children:[p.jsxs("p",{className:"text-gray-300",children:["Drag & drop files here, or"," ",p.jsx("span",{className:"text-primary-500 font-medium",children:"browse"})]}),p.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Supports PDF, DOCX, TXT, PPTX (max 10MB each)"})]})]})]}),e&&p.jsxs("div",{className:"bg-background-secondary rounded-lg p-4",children:[p.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Uploading files..."}),p.jsx("div",{className:"space-y-2"})]}),i.length>0&&p.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:[p.jsx("h4",{className:"text-red-400 font-medium mb-2",children:"Upload Errors:"}),p.jsx("ul",{className:"text-sm text-red-300 space-y-1",children:i.map((b,D)=>p.jsxs("li",{children:["• ",b]},D))})]})]})};function Ae(){return Ae=Object.assign?Object.assign.bind():function(e){for(var a=1;a<arguments.length;a++){var i=arguments[a];for(var t in i)({}).hasOwnProperty.call(i,t)&&(e[t]=i[t])}return e},Ae.apply(null,arguments)}function ta(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Re(e,a){return Re=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(i,t){return i.__proto__=t,i},Re(e,a)}function Pi(e,a){e.prototype=Object.create(a.prototype),e.prototype.constructor=e,Re(e,a)}var na=Number.isNaN||function(a){return typeof a=="number"&&a!==a};function Fi(e,a){return!!(e===a||na(e)&&na(a))}function Ai(e,a){if(e.length!==a.length)return!1;for(var i=0;i<e.length;i++)if(!Fi(e[i],a[i]))return!1;return!0}function Ee(e,a){a===void 0&&(a=Ai);var i,t=[],o,r=!1;function s(){for(var d=[],h=0;h<arguments.length;h++)d[h]=arguments[h];return r&&i===this&&a(d,t)||(o=e.apply(this,d),r=!0,i=this,t=d),o}return s}var Ri=typeof performance=="object"&&typeof performance.now=="function",oa=Ri?function(){return performance.now()}:function(){return Date.now()};function pa(e){cancelAnimationFrame(e.id)}function Ni(e,a){var i=oa();function t(){oa()-i>=a?e.call(null):o.id=requestAnimationFrame(t)}var o={id:requestAnimationFrame(t)};return o}var Ie=-1;function ra(e){if(e===void 0&&(e=!1),Ie===-1||e){var a=document.createElement("div"),i=a.style;i.width="50px",i.height="50px",i.overflow="scroll",document.body.appendChild(a),Ie=a.offsetWidth-a.clientWidth,document.body.removeChild(a)}return Ie}var Q=null;function la(e){if(e===void 0&&(e=!1),Q===null||e){var a=document.createElement("div"),i=a.style;i.width="50px",i.height="50px",i.overflow="scroll",i.direction="rtl";var t=document.createElement("div"),o=t.style;return o.width="100px",o.height="100px",a.appendChild(t),document.body.appendChild(a),a.scrollLeft>0?Q="positive-descending":(a.scrollLeft=1,a.scrollLeft===0?Q="negative":Q="positive-ascending"),document.body.removeChild(a),Q}return Q}var qi=150,Mi=function(a,i){return a};function Li(e){var a,i=e.getItemOffset,t=e.getEstimatedTotalSize,o=e.getItemSize,r=e.getOffsetForIndexAndAlignment,s=e.getStartIndexForOffset,d=e.getStopIndexForStartIndex,h=e.initInstanceProps,z=e.shouldResetStyleCacheOnItemSizeChange,b=e.validateProps;return a=function(D){Pi(k,D);function k(w){var n;return n=D.call(this,w)||this,n._instanceProps=h(n.props,ta(n)),n._outerRef=void 0,n._resetIsScrollingTimeoutId=null,n.state={instance:ta(n),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof n.props.initialScrollOffset=="number"?n.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},n._callOnItemsRendered=void 0,n._callOnItemsRendered=Ee(function(c,m,x,y){return n.props.onItemsRendered({overscanStartIndex:c,overscanStopIndex:m,visibleStartIndex:x,visibleStopIndex:y})}),n._callOnScroll=void 0,n._callOnScroll=Ee(function(c,m,x){return n.props.onScroll({scrollDirection:c,scrollOffset:m,scrollUpdateWasRequested:x})}),n._getItemStyle=void 0,n._getItemStyle=function(c){var m=n.props,x=m.direction,y=m.itemSize,E=m.layout,v=n._getItemStyleCache(z&&y,z&&E,z&&x),j;if(v.hasOwnProperty(c))j=v[c];else{var S=i(n.props,c,n._instanceProps),R=o(n.props,c,n._instanceProps),P=x==="horizontal"||E==="horizontal",M=x==="rtl",$=P?S:0;v[c]=j={position:"absolute",left:M?void 0:$,right:M?$:void 0,top:P?0:S,height:P?"100%":R,width:P?R:"100%"}}return j},n._getItemStyleCache=void 0,n._getItemStyleCache=Ee(function(c,m,x){return{}}),n._onScrollHorizontal=function(c){var m=c.currentTarget,x=m.clientWidth,y=m.scrollLeft,E=m.scrollWidth;n.setState(function(v){if(v.scrollOffset===y)return null;var j=n.props.direction,S=y;if(j==="rtl")switch(la()){case"negative":S=-y;break;case"positive-descending":S=E-x-y;break}return S=Math.max(0,Math.min(S,E-x)),{isScrolling:!0,scrollDirection:v.scrollOffset<S?"forward":"backward",scrollOffset:S,scrollUpdateWasRequested:!1}},n._resetIsScrollingDebounced)},n._onScrollVertical=function(c){var m=c.currentTarget,x=m.clientHeight,y=m.scrollHeight,E=m.scrollTop;n.setState(function(v){if(v.scrollOffset===E)return null;var j=Math.max(0,Math.min(E,y-x));return{isScrolling:!0,scrollDirection:v.scrollOffset<j?"forward":"backward",scrollOffset:j,scrollUpdateWasRequested:!1}},n._resetIsScrollingDebounced)},n._outerRefSetter=function(c){var m=n.props.outerRef;n._outerRef=c,typeof m=="function"?m(c):m!=null&&typeof m=="object"&&m.hasOwnProperty("current")&&(m.current=c)},n._resetIsScrollingDebounced=function(){n._resetIsScrollingTimeoutId!==null&&pa(n._resetIsScrollingTimeoutId),n._resetIsScrollingTimeoutId=Ni(n._resetIsScrolling,qi)},n._resetIsScrolling=function(){n._resetIsScrollingTimeoutId=null,n.setState({isScrolling:!1},function(){n._getItemStyleCache(-1,null)})},n}k.getDerivedStateFromProps=function(n,c){return $i(n,c),b(n),null};var g=k.prototype;return g.scrollTo=function(n){n=Math.max(0,n),this.setState(function(c){return c.scrollOffset===n?null:{scrollDirection:c.scrollOffset<n?"forward":"backward",scrollOffset:n,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},g.scrollToItem=function(n,c){c===void 0&&(c="auto");var m=this.props,x=m.itemCount,y=m.layout,E=this.state.scrollOffset;n=Math.max(0,Math.min(n,x-1));var v=0;if(this._outerRef){var j=this._outerRef;y==="vertical"?v=j.scrollWidth>j.clientWidth?ra():0:v=j.scrollHeight>j.clientHeight?ra():0}this.scrollTo(r(this.props,n,c,E,this._instanceProps,v))},g.componentDidMount=function(){var n=this.props,c=n.direction,m=n.initialScrollOffset,x=n.layout;if(typeof m=="number"&&this._outerRef!=null){var y=this._outerRef;c==="horizontal"||x==="horizontal"?y.scrollLeft=m:y.scrollTop=m}this._callPropsCallbacks()},g.componentDidUpdate=function(){var n=this.props,c=n.direction,m=n.layout,x=this.state,y=x.scrollOffset,E=x.scrollUpdateWasRequested;if(E&&this._outerRef!=null){var v=this._outerRef;if(c==="horizontal"||m==="horizontal")if(c==="rtl")switch(la()){case"negative":v.scrollLeft=-y;break;case"positive-ascending":v.scrollLeft=y;break;default:var j=v.clientWidth,S=v.scrollWidth;v.scrollLeft=S-j-y;break}else v.scrollLeft=y;else v.scrollTop=y}this._callPropsCallbacks()},g.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&pa(this._resetIsScrollingTimeoutId)},g.render=function(){var n=this.props,c=n.children,m=n.className,x=n.direction,y=n.height,E=n.innerRef,v=n.innerElementType,j=n.innerTagName,S=n.itemCount,R=n.itemData,P=n.itemKey,M=P===void 0?Mi:P,$=n.layout,ee=n.outerElementType,K=n.outerTagName,C=n.style,A=n.useIsScrolling,ge=n.width,ae=this.state.isScrolling,W=x==="horizontal"||$==="horizontal",F=W?this._onScrollHorizontal:this._onScrollVertical,pe=this._getRangeToRender(),re=pe[0],X=pe[1],ie=[];if(S>0)for(var q=re;q<=X;q++)ie.push(f.createElement(c,{data:R,key:M(q,R),index:q,isScrolling:A?ae:void 0,style:this._getItemStyle(q)}));var te=t(this.props,this._instanceProps);return f.createElement(ee||K||"div",{className:m,onScroll:F,ref:this._outerRefSetter,style:Ae({position:"relative",height:y,width:ge,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:x},C)},f.createElement(v||j||"div",{children:ie,ref:E,style:{height:W?"100%":te,pointerEvents:ae?"none":void 0,width:W?te:"100%"}}))},g._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var n=this.props.itemCount;if(n>0){var c=this._getRangeToRender(),m=c[0],x=c[1],y=c[2],E=c[3];this._callOnItemsRendered(m,x,y,E)}}if(typeof this.props.onScroll=="function"){var v=this.state,j=v.scrollDirection,S=v.scrollOffset,R=v.scrollUpdateWasRequested;this._callOnScroll(j,S,R)}},g._getRangeToRender=function(){var n=this.props,c=n.itemCount,m=n.overscanCount,x=this.state,y=x.isScrolling,E=x.scrollDirection,v=x.scrollOffset;if(c===0)return[0,0,0,0];var j=s(this.props,v,this._instanceProps),S=d(this.props,j,v,this._instanceProps),R=!y||E==="backward"?Math.max(1,m):1,P=!y||E==="forward"?Math.max(1,m):1;return[Math.max(0,j-R),Math.max(0,Math.min(c-1,S+P)),j,S]},k}(f.PureComponent),a.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},a}var $i=function(a,i){a.children,a.direction,a.height,a.layout,a.innerTagName,a.outerTagName,a.width,i.instance},Wi=Li({getItemOffset:function(a,i){var t=a.itemSize;return i*t},getItemSize:function(a,i){var t=a.itemSize;return t},getEstimatedTotalSize:function(a){var i=a.itemCount,t=a.itemSize;return t*i},getOffsetForIndexAndAlignment:function(a,i,t,o,r,s){var d=a.direction,h=a.height,z=a.itemCount,b=a.itemSize,D=a.layout,k=a.width,g=d==="horizontal"||D==="horizontal",w=g?k:h,n=Math.max(0,z*b-w),c=Math.min(n,i*b),m=Math.max(0,i*b-w+b+s);switch(t==="smart"&&(o>=m-w&&o<=c+w?t="auto":t="center"),t){case"start":return c;case"end":return m;case"center":{var x=Math.round(m+(c-m)/2);return x<Math.ceil(w/2)?0:x>n+Math.floor(w/2)?n:x}case"auto":default:return o>=m&&o<=c?o:o<m?m:c}},getStartIndexForOffset:function(a,i){var t=a.itemCount,o=a.itemSize;return Math.max(0,Math.min(t-1,Math.floor(i/o)))},getStopIndexForStartIndex:function(a,i,t){var o=a.direction,r=a.height,s=a.itemCount,d=a.itemSize,h=a.layout,z=a.width,b=o==="horizontal"||h==="horizontal",D=i*d,k=b?z:r,g=Math.ceil((k+t-D)/d);return Math.max(0,Math.min(s-1,i+g-1))},initInstanceProps:function(a){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(a){a.itemSize}});const Da=({document:e})=>{const{selectedDocuments:a,toggleDocumentSelection:i,deleteDocument:t}=Ne(),{confirm:o,alert:r}=ca(),[s,d]=f.useState(!1),h=a.has(e.id),z=async()=>{if(await o({title:"Delete Document",message:`Are you sure you want to delete "${e.filename}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger"})){d(!0);try{await t(e.id)}catch(w){console.error("Delete error:",w),await r({title:"Delete Error",message:"Failed to delete document. Please try again.",variant:"error"})}finally{d(!1)}}},b=g=>{if(g===0)return"0 Bytes";const w=1024,n=["Bytes","KB","MB","GB"],c=Math.floor(Math.log(g)/Math.log(w));return parseFloat((g/Math.pow(w,c)).toFixed(2))+" "+n[c]},D=g=>new Date(g).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),k=g=>({pdf:"📄",docx:"📝",txt:"📃",pptx:"📊"})[g]||"📄";return p.jsxs("div",{className:`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${h?"border-primary-500 bg-primary-500/10":"border-gray-700 hover:border-gray-600"}
      `,onClick:()=>i(e.id),children:[p.jsxs("div",{className:"flex items-start justify-between mb-3",children:[p.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[p.jsx("span",{className:"text-2xl",children:k(e.file_type)}),p.jsxs("div",{className:"min-w-0 flex-1",children:[p.jsx("h3",{className:"text-white font-medium truncate",title:e.filename,children:e.filename}),p.jsxs("p",{className:"text-sm text-gray-400",children:[e.file_type.toUpperCase()," • ",b(e.file_size)]})]})]}),p.jsx("div",{className:`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${h?"bg-primary-500 border-primary-500":"border-gray-500"}
          `,children:h&&p.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),!e.is_processed&&p.jsx("div",{className:"mb-3",children:p.jsxs("div",{className:"flex items-center space-x-2 text-yellow-400",children:[p.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),p.jsx("span",{className:"text-sm",children:"Processing..."})]})}),e.processing_error&&p.jsx("div",{className:"mb-3",children:p.jsxs("div",{className:"text-red-400 text-sm",children:["⚠️ Processing failed: ",e.processing_error]})}),p.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Uploaded ",D(e.uploaded_at)]}),p.jsx("div",{className:"flex justify-end space-x-2",onClick:g=>g.stopPropagation(),children:p.jsx(J,{onClick:z,variant:"danger",size:"sm",isLoading:s,children:"Delete"})})]})},Sa=f.memo(({index:e,style:a,data:i})=>{const{documents:t}=i,o=t[e];return p.jsx("div",{style:a,children:p.jsx("div",{className:"px-2 py-1",children:p.jsx(Da,{document:o})})})});Sa.displayName="DocumentItem";const za=f.memo(({documents:e,height:a=400,itemHeight:i=120})=>{const t=f.useMemo(()=>({documents:e}),[e]);return e.length===0?p.jsxs("div",{className:"text-center py-12",children:[p.jsx("div",{className:"text-gray-400 mb-4",children:"No documents uploaded yet"}),p.jsx("p",{className:"text-sm text-gray-500",children:"Upload your first document to get started with AI-powered study materials."})]}):e.length<=10?p.jsx("div",{className:"space-y-4",children:e.map(o=>p.jsx(Da,{document:o},o.id))}):p.jsx("div",{className:"border border-gray-600 rounded-lg overflow-hidden",children:p.jsx(Wi,{height:a,width:"100%",itemCount:e.length,itemSize:i,itemData:t,overscanCount:5,children:Sa})})});za.displayName="VirtualizedDocumentList";const Ui=f.memo(()=>{const{documents:e,selectedDocuments:a,isLoading:i,fetchDocuments:t,searchDocuments:o,clearSelection:r,selectAll:s,deleteDocument:d}=Ne(),{confirm:h,alert:z}=ca(),[b,D]=f.useState(""),[k,g]=f.useState(null),[w,n]=f.useState(!1);f.useEffect(()=>{t()},[t]);const c=async()=>{if(b.trim().length<2){g(null);return}n(!0);try{const v=await o(b.trim());g(v)}catch(v){console.error("Search error:",v)}finally{n(!1)}},m=()=>{D(""),g(null)},x=async()=>{if(!(a.size===0||!await h({title:"Delete Documents",message:`Are you sure you want to delete ${a.size} document(s)? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger"})))try{const j=Array.from(a).map(S=>d(S));await Promise.all(j),r()}catch(j){console.error("Bulk delete error:",j),await z({title:"Delete Error",message:"Some documents could not be deleted. Please try again.",variant:"error"})}},y=k||e,E=a.size>0;return i&&e.length===0?p.jsx("div",{className:"flex items-center justify-center py-12",children:p.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[p.jsx("div",{className:"flex-1",children:p.jsxs("div",{className:"flex gap-2",children:[p.jsx(Pa,{placeholder:"Search documents...",value:b,onChange:D}),p.jsx(J,{onClick:c,isLoading:w,disabled:b.trim().length<2,children:"Search"}),k&&p.jsx(J,{onClick:m,variant:"secondary",children:"Clear"})]})}),E&&p.jsxs("div",{className:"flex gap-2",children:[p.jsx(J,{onClick:s,variant:"secondary",size:"sm",children:"Select All"}),p.jsxs(J,{onClick:r,variant:"secondary",size:"sm",children:["Clear (",a.size,")"]}),p.jsx(J,{onClick:x,variant:"danger",size:"sm",children:"Delete Selected"})]})]}),k&&p.jsxs("div",{className:"text-sm text-gray-400",children:["Found ",k.length,' document(s) matching "',b,'"']}),p.jsx(za,{documents:y,height:600})]})}),Ki=()=>p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:p.jsxs("div",{className:"space-y-8",children:[p.jsxs("div",{children:[p.jsx("h1",{className:"text-3xl font-bold text-white",children:"Documents"}),p.jsx("p",{className:"mt-2 text-gray-400",children:"Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX"})]}),p.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[p.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Upload Documents"}),p.jsx(Ci,{})]}),p.jsxs("div",{children:[p.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Documents"}),p.jsx(Ui,{})]})]})});export{Ki as DocumentsPage};
