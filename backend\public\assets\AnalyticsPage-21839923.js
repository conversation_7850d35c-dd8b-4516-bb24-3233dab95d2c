import{r as u,j as e}from"./index-ae0554d4.js";const x=n=>{const r=Math.floor(n/3600),a=Math.floor(n%3600/60),t=n%60;return r>0?`${r}h ${a}m`:a>0?`${a}m ${t}s`:`${t}s`},o=u.memo(({title:n,value:r,subtitle:a,icon:t})=>e.jsx("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-400",children:n}),e.jsx("p",{className:"text-2xl font-semibold text-white mt-1",children:r}),a&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:a})]}),t&&e.jsx("div",{className:"text-2xl",children:t})]})}));o.displayName="StatCard";const w=u.memo(({sessions:n})=>{const r=u.useMemo(()=>{const t=Array.from({length:7},(d,l)=>{const c=new Date;return c.setDate(c.getDate()-(6-l)),{date:c.toLocaleDateString("en-US",{weekday:"short"}),sessions:0,timeSpent:0}});return n.forEach(d=>{const l=new Date(d.startTime),c=Math.floor((Date.now()-l.getTime())/(1e3*60*60*24));if(c>=0&&c<7){const m=6-c;t[m].sessions+=1,t[m].timeSpent+=d.timeSpent}}),t},[n]),a=Math.max(...r.map(t=>t.timeSpent));return e.jsxs("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Study Activity (Last 7 Days)"}),e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:r.map((t,d)=>e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"100px"},children:e.jsx("div",{className:"bg-primary-500 rounded-t transition-all duration-300",style:{height:a>0?`${t.timeSpent/a*100}%`:"0%",position:"absolute",bottom:0,left:0,right:0},title:`${t.sessions} sessions, ${x(t.timeSpent)}`})}),e.jsx("div",{className:"text-xs text-gray-400 mt-2",children:t.date})]},d))})]})});w.displayName="ProgressChart";const v=u.memo(({studySets:n,sessions:r})=>{const a=u.useMemo(()=>{var S,p;const t=r.reduce((s,i)=>s+i.timeSpent,0),d=r.length,l=d>0?t/d:0,c=r.reduce((s,i)=>s+i.reviewedItems,0),m=r.filter(s=>s.type==="quiz"&&s.correctAnswers!==void 0),g=m.reduce((s,i)=>s+i.totalItems,0),f=m.reduce((s,i)=>s+(i.correctAnswers||0),0),j=g>0?f/g*100:0,y=new Date;let h=0;for(let s=0;s<365;s++){const i=new Date(y);if(i.setDate(y.getDate()-s),r.some(T=>new Date(T.startTime).toDateString()===i.toDateString()))h++;else if(s>0)break}const D=r.reduce((s,i)=>(s[i.studySetId]=(s[i.studySetId]||0)+1,s),{}),b=(S=Object.entries(D).sort(([,s],[,i])=>i-s)[0])==null?void 0:S[0],I=((p=n.find(s=>s.id===b))==null?void 0:p.name)||"None",N=r.sort((s,i)=>new Date(i.startTime).getTime()-new Date(s.startTime).getTime()).slice(0,5);return{totalStudyTime:t,totalSessions:d,averageSessionTime:l,totalItemsReviewed:c,averageAccuracy:j,studyStreak:h,mostStudiedSet:I,recentActivity:N}},[r,n]);return e.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Study Analytics"}),e.jsx("p",{className:"text-gray-400",children:"Track your learning progress and performance"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(o,{title:"Total Study Time",value:x(a.totalStudyTime),icon:"⏱️"}),e.jsx(o,{title:"Study Sessions",value:a.totalSessions,subtitle:"All time",icon:"📚"}),e.jsx(o,{title:"Average Session",value:x(Math.round(a.averageSessionTime)),icon:"⏰"}),e.jsx(o,{title:"Study Streak",value:`${a.studyStreak} days`,icon:"🔥"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsx(o,{title:"Items Reviewed",value:a.totalItemsReviewed,subtitle:"Flashcards & questions",icon:"✅"}),e.jsx(o,{title:"Quiz Accuracy",value:`${Math.round(a.averageAccuracy)}%`,subtitle:"Average across all quizzes",icon:"🎯"})]}),e.jsx("div",{className:"mb-8",children:e.jsx(w,{sessions:r})}),e.jsxs("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Activity"}),a.recentActivity.length===0?e.jsx("p",{className:"text-gray-400",children:"No recent study sessions"}):e.jsx("div",{className:"space-y-3",children:a.recentActivity.map((t,d)=>{const l=n.find(c=>c.id===t.studySetId);return e.jsxs("div",{className:"flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-white font-medium",children:(l==null?void 0:l.name)||"Unknown Set"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[t.type==="quiz"?"Quiz":"Flashcards"," • ",t.reviewedItems,"/",t.totalItems," items"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm text-gray-300",children:x(t.timeSpent)}),e.jsx("p",{className:"text-xs text-gray-500",children:new Date(t.startTime).toLocaleDateString()})]})]},d)})})]})]})});v.displayName="StudyAnalytics";const A=[{id:"1",user_id:"mock-user",name:"Spanish Vocabulary",type:"flashcards",is_ai_generated:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"2",user_id:"mock-user",name:"Math Formulas",type:"flashcards",is_ai_generated:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{id:"3",user_id:"mock-user",name:"History Facts",type:"quiz",is_ai_generated:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}],k=[{id:"1",studySetId:"1",type:"flashcards",startTime:new Date(Date.now()-864e5),totalItems:20,reviewedItems:18,flaggedItems:3,timeSpent:1200},{id:"2",studySetId:"2",type:"quiz",startTime:new Date(Date.now()-1728e5),totalItems:15,reviewedItems:15,flaggedItems:0,correctAnswers:12,timeSpent:900},{id:"3",studySetId:"1",type:"flashcards",startTime:new Date(Date.now()-2592e5),totalItems:20,reviewedItems:20,flaggedItems:5,timeSpent:1800},{id:"4",studySetId:"3",type:"quiz",startTime:new Date(Date.now()-3456e5),totalItems:25,reviewedItems:25,flaggedItems:0,correctAnswers:20,timeSpent:1500},{id:"5",studySetId:"2",type:"flashcards",startTime:new Date(Date.now()-432e6),totalItems:15,reviewedItems:12,flaggedItems:2,timeSpent:600}],M=()=>e.jsx(v,{studySets:A,sessions:k});export{M as AnalyticsPage};
