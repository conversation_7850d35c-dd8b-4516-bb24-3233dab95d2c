import{a as B,b as O,r as o,j as e,B as N,d as L,u as U}from"./index-ae0554d4.js";import{u as V}from"./studyStore-55f3de13.js";import{D as R}from"./DocumentSelector-7362a9da.js";import"./documentStore-75b42437.js";const J=({studySetId:m,flashcards:f,onFlashcardAdded:l,onFlashcardUpdated:I,onFlashcardDeleted:T,onFlashcardsGenerated:u})=>{const{alert:d,confirm:S,prompt:C}=B(),{user:x}=O(),[b,_]=o.useState(!1),[p,F]=o.useState([]),[j,y]=o.useState(10),[k,h]=o.useState(""),[v,$]=o.useState(!1),[a,g]=o.useState(!1),[w,E]=o.useState({front:"",back:"",difficulty_level:3}),D=async()=>{if(!w.front.trim()||!w.back.trim()){await d({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const t=await fetch(`/api/flashcards/study-set/${m}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({front:w.front.trim(),back:w.back.trim(),difficulty_level:w.difficulty_level,is_ai_generated:!1})});if(!t.ok)throw new Error("Failed to create flashcard");const s=await t.json();l(s.data),E({front:"",back:"",difficulty_level:3}),g(!1),await d({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(t){await d({title:"Error",message:t.message||"Failed to add flashcard",variant:"error"})}},z=async t=>{const s=await C({title:"Edit Front",message:"Enter the front content:",defaultValue:t.front});if(s===null)return;const r=await C({title:"Edit Back",message:"Enter the back content:",defaultValue:t.back});if(r!==null)try{const i=await fetch(`/api/flashcards/${t.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({front:s.trim(),back:r.trim()})});if(!i.ok)throw new Error("Failed to update flashcard");const q=await i.json();I(q.data),await d({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(i){await d({title:"Error",message:i.message||"Failed to update flashcard",variant:"error"})}},G=async t=>{if(await S({title:"Delete Flashcard",message:`Are you sure you want to delete this flashcard?

Front: ${t.front.substring(0,50)}${t.front.length>50?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/flashcards/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok)throw new Error("Failed to delete flashcard");T(t.id),await d({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(r){await d({title:"Error",message:r.message||"Failed to delete flashcard",variant:"error"})}},Q=()=>j*2,M=async()=>{if(p.length===0){await d({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});return}const t=Q();if(x&&x.credits_remaining<t){await d({title:"Insufficient Credits",message:`You need ${t} credits to generate ${j} flashcards, but you only have ${x.credits_remaining} credits remaining.`,variant:"error"});return}if(await S({title:"Generate Flashcards",message:`Generate ${j} flashcards from ${p.length} document(s)?

This will cost ${t} credits.`,confirmText:"Generate",cancelText:"Cancel"})){$(!0);try{const r=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({studySetId:m,documentIds:p,count:j,customPrompt:k.trim()||void 0})});if(!r.ok)throw new Error("Failed to generate flashcards");const i=await r.json();u(i.data.flashcards),x&&O.getState().updateUser({credits_remaining:i.data.creditsRemaining}),await d({title:"Success",message:`Generated ${i.data.flashcards.length} flashcards successfully!`,variant:"success"}),F([]),h(""),_(!1)}catch(r){await d({title:"Generation Error",message:r.message||"Failed to generate flashcards",variant:"error"})}finally{$(!1)}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(N,{onClick:()=>g(!a),variant:"secondary",size:"sm",children:"➕ Add Flashcard"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"AI Mode"}),e.jsx("button",{onClick:()=>_(!b),className:`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${b?"bg-primary-500":"bg-gray-600"}
              `,children:e.jsx("span",{className:`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${b?"translate-x-6":"translate-x-1"}
                `})})]})]})]}),a&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:w.front,onChange:t=>E(s=>({...s,front:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:w.back,onChange:t=>E(s=>({...s,back:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:w.difficulty_level,onChange:t=>E(s=>({...s,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(N,{onClick:D,variant:"primary",children:"Add Flashcard"}),e.jsx(N,{onClick:()=>g(!1),variant:"secondary",children:"Cancel"})]})]})]}),b&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(R,{selectedDocuments:p,onSelectionChange:F,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Flashcards"}),e.jsx("input",{type:"number",min:"1",max:"50",value:j,onChange:t=>y(parseInt(t.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[Q()," credits"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:k,onChange:t=>h(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),e.jsx(N,{onClick:M,disabled:p.length===0||v,className:"w-full",variant:"primary",children:v?"Generating...":`Generate ${j} Flashcards`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",f.length,")"]}),f.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:f.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),e.jsx("p",{className:"text-white font-medium",children:t.front})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),e.jsx("p",{className:"text-gray-300",children:t.back})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",t.difficulty_level,"/5"]}),e.jsxs("span",{children:["Reviewed: ",t.times_reviewed||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>z(t),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"✏️"}),e.jsx("button",{onClick:()=>G(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"🗑️"})]})]})},t.id))})]})]})},H=({studySetId:m,questions:f,onQuestionAdded:l,onQuestionDeleted:I,onQuestionsGenerated:T})=>{const{alert:u,confirm:d}=B(),{user:S}=O(),[C,x]=o.useState(!1),[b,_]=o.useState([]),[p,F]=o.useState(10),[j,y]=o.useState(""),[k,h]=o.useState(!1),[v,$]=o.useState(!1),[a,g]=o.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),w=async()=>{if(!a.question_text.trim()){await u({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(a.correct_answers.length===0){await u({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((a.question_type==="multiple_choice"||a.question_type==="select_all")&&a.options.filter(s=>s.trim().length>0).length<2){await u({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/study-set/${m}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({question_text:a.question_text.trim(),question_type:a.question_type,options:a.question_type==="multiple_choice"||a.question_type==="select_all"?a.options.filter(r=>r.trim().length>0):null,correct_answers:a.correct_answers,explanation:a.explanation.trim()||null,difficulty_level:a.difficulty_level})});if(!t.ok)throw new Error("Failed to create question");const s=await t.json();l(s.data),g({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),$(!1),await u({title:"Success",message:"Question added successfully!",variant:"success"})}catch(t){await u({title:"Error",message:t.message||"Failed to add question",variant:"error"})}},E=async t=>{if(await d({title:"Delete Question",message:`Are you sure you want to delete this question?

${t.question_text.substring(0,100)}${t.question_text.length>100?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/quiz-questions/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok)throw new Error("Failed to delete question");I(t.id),await u({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(r){await u({title:"Error",message:r.message||"Failed to delete question",variant:"error"})}},D=()=>p*3,z=async()=>{if(b.length===0){await u({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});return}const t=D();if(S&&S.credits_remaining<t){await u({title:"Insufficient Credits",message:`You need ${t} credits to generate ${p} questions, but you only have ${S.credits_remaining} credits remaining.`,variant:"warning"});return}if(await d({title:"Generate Questions",message:`Generate ${p} questions for ${t} credits?`,confirmText:"Generate",cancelText:"Cancel"})){h(!0);try{const r=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({studySetId:m,documentIds:b,count:p,customPrompt:j.trim()||void 0})});if(!r.ok)throw new Error("Failed to generate questions");const i=await r.json();T(i.data.questions),S&&O.getState().updateUser({credits_remaining:i.data.creditsRemaining}),await u({title:"Success",message:`Generated ${i.data.questions.length} questions successfully!`,variant:"success"}),_([]),y(""),x(!1)}catch(r){await u({title:"Error",message:r.message||"Failed to generate questions",variant:"error"})}finally{h(!1)}}},G=t=>{g(s=>({...s,question_type:t,options:t==="multiple_choice"||t==="select_all"?["","","",""]:[],correct_answers:[]}))},Q=(t,s)=>{g(r=>({...r,options:r.options.map((i,q)=>q===t?s:i)}))},M=t=>{g(s=>{const r=s.correct_answers.includes(t);return s.question_type==="multiple_choice"?{...s,correct_answers:r?[]:[t]}:{...s,correct_answers:r?s.correct_answers.filter(i=>i!==t):[...s.correct_answers,t]}})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(N,{onClick:()=>$(!v),variant:"secondary",size:"sm",children:v?"Cancel":"Add Question"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"AI Mode"}),e.jsx("button",{onClick:()=>x(!C),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${C?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${C?"translate-x-6":"translate-x-1"}`})})]})]})]}),v&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:a.question_text,onChange:t=>g(s=>({...s,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:a.question_type,onChange:t=>G(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(a.question_type==="multiple_choice"||a.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:a.options.map((t,s)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>M(t),className:`flex-shrink-0 w-5 h-5 border-2 ${a.question_type==="multiple_choice"?"rounded-full":"rounded"} ${a.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:a.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:r=>Q(s,r.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${s+1}`})]},s))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:a.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),a.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>g(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${a.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>g(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${a.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),a.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:a.correct_answers.join(`
`),onChange:t=>g(s=>({...s,correct_answers:t.target.value.split(`
`).filter(r=>r.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:a.explanation,onChange:t=>g(s=>({...s,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:a.difficulty_level,onChange:t=>g(s=>({...s,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(N,{onClick:w,variant:"primary",children:"Add Question"}),e.jsx(N,{onClick:()=>$(!1),variant:"secondary",children:"Cancel"})]})]})]}),C&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(R,{selectedDocuments:b,onSelectionChange:_,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Questions"}),e.jsx("input",{type:"number",min:"1",max:"50",value:p,onChange:t=>F(parseInt(t.target.value)||1),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[D()," credits"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:j,onChange:t=>y(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),e.jsx(N,{onClick:z,disabled:b.length===0||k,className:"w-full",variant:"primary",children:k?"Generating...":`Generate ${p} Questions`})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Questions (",f.length,")"]}),f.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:f.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),e.jsx("p",{className:"text-white font-medium",children:t.question_text})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),e.jsx("p",{className:"text-gray-300 capitalize",children:t.question_type.replace("_"," ")})]}),t.options&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),e.jsx("ul",{className:"text-gray-300 text-sm",children:t.options.map((s,r)=>e.jsxs("li",{className:`${t.correct_answers.includes(s)?"text-green-400 font-medium":""}`,children:[r+1,". ",s]},r))})]}),t.explanation&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),e.jsx("p",{className:"text-gray-300 text-sm",children:t.explanation})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty: ",t.difficulty_level,"/5"]}),e.jsxs("span",{children:["Attempted: ",t.times_attempted||0," times"]}),e.jsxs("span",{children:["Correct: ",t.times_correct||0," times"]})]})]}),e.jsx("div",{className:"flex items-center space-x-2 ml-4",children:e.jsx("button",{onClick:()=>E(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"🗑️"})})]})},t.id))})]})]})},Z=()=>{const{id:m}=L(),f=U(),{studySetContent:l,isLoading:I,error:T,fetchStudySetContent:u}=V(),{alert:d,confirm:S,prompt:C}=B(),[x,b]=o.useState(null),[_,p]=o.useState("study"),[F,j]=o.useState("flashcards"),[y,k]=o.useState([]),[h,v]=o.useState([]),[$,a]=o.useState("");o.useEffect(()=>{m&&u(m).catch(console.error)},[m,u]),o.useEffect(()=>{l!=null&&l.studySet&&(a(l.studySet.name),k(l.flashcards||[]),v(l.questions||[]))},[l]);const g=async()=>{if(!(!m||!x))try{await V.getState().startStudySession(m,x),f(`/study/${m}/${x}`)}catch(n){await d({title:"Error",message:n.message||"Failed to start study session",variant:"error"})}},w=async()=>{if(!m||!(l!=null&&l.studySet))return;const n=await C({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:l.studySet.name});if(!(n===null||n.trim()===l.studySet.name)){if(!n.trim()){await d({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"});return}try{if(!(await fetch(`/api/study-sets/${m}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({name:n.trim()})})).ok)throw new Error("Failed to rename study set");a(n.trim()),await d({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await u(m)}catch(c){await d({title:"Error",message:c.message||"Failed to rename study set",variant:"error"})}}},E=async()=>{if(!(!m||!(l!=null&&l.studySet)||!await S({title:"Delete Study Set",message:`Are you sure you want to delete "${l.studySet.name}"?

This action cannot be undone and will delete all flashcards and quiz questions in this set.`,variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"})))try{if(!(await fetch(`/api/study-sets/${m}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}})).ok)throw new Error("Failed to delete study set");await d({title:"Success",message:"Study set deleted successfully!",variant:"success"}),f("/dashboard")}catch(c){await d({title:"Error",message:c.message||"Failed to delete study set",variant:"error"})}},D=n=>{k(c=>[...c,n])},z=n=>{k(c=>c.map(A=>A.id===n.id?n:A))},G=n=>{k(c=>c.filter(A=>A.id!==n))},Q=n=>{k(c=>[...c,...n])},M=n=>{v(c=>[...c,n])},t=n=>{v(c=>c.map(A=>A.id===n.id?n:A))},s=n=>{v(c=>c.filter(A=>A.id!==n))},r=n=>{v(c=>[...c,...n])};if(I)return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(T||!(l!=null&&l.studySet))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-red-400 mb-4",children:T||"Study set not found"}),e.jsx(N,{onClick:()=>f("/dashboard"),variant:"secondary",children:"Back to Dashboard"})]})});const{studySet:i}=l,q=y&&y.length>0,P=h&&h.length>0;return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("button",{onClick:()=>f("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Dashboard"}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:$}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(N,{onClick:w,variant:"secondary",size:"sm",children:"✏️ Rename"}),e.jsx(N,{onClick:E,variant:"danger",size:"sm",children:"🗑️ Delete"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[e.jsx("span",{className:"capitalize",children:i.type}),i.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.jsxs("span",{children:["Created ",new Date(i.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-600",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>p("study"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${_==="study"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"📚 Study Mode"}),e.jsx("button",{onClick:()=>p("manage"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${_==="manage"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"⚙️ Manage Content"})]})})}),_==="study"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[q&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${x==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>b("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"🃏"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[y==null?void 0:y.length," flashcards • Interactive review"]})]})]})}),P&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${x==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>b("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"📝"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[h==null?void 0:h.length," questions • Test your knowledge"]})]})]})})]}),e.jsx(N,{onClick:g,disabled:!x,className:"w-full",size:"lg",children:x?`Start ${x==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]})}),_==="manage"&&m&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex space-x-1 mb-6",children:[e.jsxs("button",{onClick:()=>j("flashcards"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${F==="flashcards"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["📚 Flashcards (",y.length,")"]}),e.jsxs("button",{onClick:()=>j("quiz"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${F==="quiz"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["❓ Quiz Questions (",h.length,")"]})]}),F==="flashcards"&&e.jsx(J,{studySetId:m,flashcards:y,onFlashcardAdded:D,onFlashcardUpdated:z,onFlashcardDeleted:G,onFlashcardsGenerated:Q}),F==="quiz"&&e.jsx(H,{studySetId:m,questions:h,onQuestionAdded:M,onQuestionUpdated:t,onQuestionDeleted:s,onQuestionsGenerated:r})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[q&&e.jsxs("div",{children:[y.length," flashcards"]}),P&&e.jsxs("div",{children:[h==null?void 0:h.length," quiz questions"]}),!q&&!P&&e.jsx("div",{className:"text-gray-500",children:"No content yet"})]})]}),i.source_documents&&i.source_documents.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),e.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:i.source_documents.map((n,c)=>e.jsx("div",{children:n.filename},c))})]}),i.custom_prompt&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),e.jsx("p",{className:"text-sm text-gray-400",children:i.custom_prompt})]})]})]})]})};export{Z as StudySetPage};
