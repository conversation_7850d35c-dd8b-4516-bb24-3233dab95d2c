import{c as l}from"./index-ae0554d4.js";const f=l((c,s)=>({currentSession:null,studySetContent:null,isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async(e,t=!1)=>{var r;const{studySetContent:n}=s();if(!(t&&((r=n==null?void 0:n.studySet)==null?void 0:r.id)===e)){c({isLoading:!0,error:null});try{const o=localStorage.getItem("auth_token"),i=await fetch(`/api/study-sets/${e}/content`,{headers:{Authorization:`Bearer ${o}`}});if(!i.ok){const d=await i.json();throw new Error(d.error||"Failed to fetch study set content")}const a=await i.json();if(a.success)c({studySetContent:{studySet:a.data.studySet,flashcards:a.data.studySet.type==="flashcards"?a.data.content:void 0,questions:a.data.studySet.type==="quiz"?a.data.content:void 0},isLoading:!1});else throw new Error(a.error)}catch(o){throw c({error:o.message||"Failed to fetch study set content",isLoading:!1}),o}}},startStudySession:async(e,t)=>{var a,d,u;const{studySetContent:n,fetchStudySetContent:r}=s();(!n||((a=n.studySet)==null?void 0:a.id)!==e)&&await r(e);const o=s().studySetContent;if(!o)throw new Error("Failed to load study set content");const i=t==="flashcards"?((d=o.flashcards)==null?void 0:d.length)||0:((u=o.questions)==null?void 0:u.length)||0;if(i===0)throw new Error("No study materials found in this set");c({currentSession:{studySetId:e,type:t,startTime:new Date,currentIndex:0,totalItems:i,reviewedItems:[],flaggedItems:[],correctAnswers:t==="quiz"?0:void 0,timeSpent:0}})},endStudySession:()=>{c({currentSession:null})},nextItem:()=>{const{currentSession:e,addToHistory:t}=s();if(!e)return;const n=e.currentIndex===e.totalItems-1?0:e.currentIndex+1;t({type:"NEXT_ITEM",payload:{fromIndex:e.currentIndex,toIndex:n},previousState:{currentIndex:e.currentIndex},timestamp:Date.now()}),c({currentSession:{...e,currentIndex:n}})},previousItem:()=>{const{currentSession:e,addToHistory:t}=s();if(!e)return;const n=e.currentIndex===0?e.totalItems-1:e.currentIndex-1;t({type:"PREVIOUS_ITEM",payload:{fromIndex:e.currentIndex,toIndex:n},previousState:{currentIndex:e.currentIndex},timestamp:Date.now()}),c({currentSession:{...e,currentIndex:n}})},goToItem:e=>{const{currentSession:t}=s();if(!t)return;const n=Math.max(0,Math.min(e,t.totalItems-1));c({currentSession:{...t,currentIndex:n}})},toggleFlag:e=>{const{currentSession:t,addToHistory:n}=s();if(!t)return;const r=t.flaggedItems.includes(e),o=r?t.flaggedItems.filter(i=>i!==e):[...t.flaggedItems,e];n({type:"TOGGLE_FLAG",payload:{itemId:e,wasFlagged:r},previousState:{flaggedItems:t.flaggedItems},timestamp:Date.now()}),c({currentSession:{...t,flaggedItems:o}})},markReviewed:e=>{const{currentSession:t}=s();t&&(t.reviewedItems.includes(t.currentIndex)||c({currentSession:{...t,reviewedItems:[...t.reviewedItems,t.currentIndex]}}))},submitQuizAnswer:(e,t,n)=>{const{currentSession:r,markReviewed:o}=s();!r||r.type!=="quiz"||(o(e),n&&c({currentSession:{...r,correctAnswers:(r.correctAnswers||0)+1}}))},updateTimeSpent:e=>{const{currentSession:t}=s();t&&c({currentSession:{...t,timeSpent:t.timeSpent+e}})},addToHistory:e=>{const{actionHistory:t,currentActionIndex:n}=s(),r=t.slice(0,n+1);r.push(e);const o=r.slice(-50);c({actionHistory:o,currentActionIndex:o.length-1,canUndo:o.length>0,canRedo:!1})},undo:()=>{const{actionHistory:e,currentActionIndex:t,currentSession:n}=s();if(t<0||!n)return;const r=e[t];c({currentSession:{...n,...r.previousState},currentActionIndex:t-1,canUndo:t>0,canRedo:!0})},redo:()=>{const{actionHistory:e,currentActionIndex:t,currentSession:n}=s();if(t>=e.length-1||!n)return;const r=t+1,o=e[r];switch(o.type){case"NEXT_ITEM":s().nextItem();break;case"PREVIOUS_ITEM":s().previousItem();break;case"TOGGLE_FLAG":s().toggleFlag(o.payload.itemId);break;case"MARK_REVIEWED":s().markReviewed(o.payload.itemId);break}c({currentActionIndex:r,canUndo:!0,canRedo:r<e.length-1})},clearHistory:()=>{c({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})}}));export{f as u};
