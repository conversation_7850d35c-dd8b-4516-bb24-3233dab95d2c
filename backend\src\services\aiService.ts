import axios from 'axios';
import { Flashcard, QuizQuestion } from '../../../shared/types';

export class AIService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://openrouter.ai/api/v1';
  private readonly model = 'minimax/minimax-m1';

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY!;
    if (!this.apiKey) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }
  }

  async generateFlashcards(
    content: string,
    count: number = 10,
    customPrompt?: string
  ): Promise<Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'created_at' | 'updated_at'>[]> {
    const prompt = this.buildFlashcardPrompt(content, count, customPrompt);
    
    try {
      const response = await this.callOpenRouter(prompt);
      return this.parseFlashcardResponse(response);
    } catch (error) {
      throw new AIError('Flashcard generation failed', error);
    }
  }

  async generateQuizQuestions(
    content: string,
    count: number = 10,
    customPrompt?: string
  ): Promise<Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[]> {
    let attempts = 0;
    const maxAttempts = 2;

    while (attempts < maxAttempts) {
      attempts++;

      // Reduce question count on retry to avoid truncation
      const adjustedCount = attempts > 1 ? Math.max(5, Math.floor(count * 0.7)) : count;
      const prompt = this.buildQuizPrompt(content, adjustedCount, customPrompt);

      try {
        console.log(`Quiz generation attempt ${attempts} with ${adjustedCount} questions`);
        const response = await this.callOpenRouter(prompt);
        console.log(`Received response length: ${response.length} characters`);

        const questions = this.parseQuizResponse(response);
        console.log(`Successfully parsed ${questions.length} questions`);

        if (questions.length > 0) {
          return questions;
        }

        throw new Error('No valid questions generated');
      } catch (error) {
        console.error(`Quiz generation attempt ${attempts} failed:`, error instanceof Error ? error.message : error);

        if (attempts >= maxAttempts) {
          throw new AIError('Quiz generation failed after multiple attempts', error);
        }

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new AIError('Quiz generation failed after all attempts');
  }

  private buildFlashcardPrompt(content: string, count: number, customPrompt?: string): string {
    const basePrompt = `
Create ${count} high-quality flashcards from the following content. Each flashcard should have a clear, concise front (question/term) and a comprehensive back (answer/definition).

Guidelines:
- Focus on key concepts, definitions, and important facts
- Make questions specific and answerable
- Ensure answers are complete but concise
- Vary question types (definitions, explanations, examples, applications)
- Avoid overly simple or overly complex questions
- Use clear, educational language

${customPrompt ? `Additional instructions: ${customPrompt}\n` : ''}

Content to process:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

Return ONLY a valid JSON array with this exact structure:
[
  {
    "front": "Question or term here",
    "back": "Answer or definition here",
    "difficulty_level": 1-5
  }
]

Ensure the response is valid JSON that can be parsed directly.`;

    return basePrompt;
  }

  private buildQuizPrompt(content: string, count: number, customPrompt?: string): string {
    const basePrompt = `
Create ${count} diverse quiz questions from the following content. Include multiple choice, select all that apply, true/false, and short answer questions.

Guidelines:
- Create a mix of question types
- Ensure all questions are answerable from the content
- Make distractors plausible but clearly incorrect
- Include 3-4 options for multiple choice questions
- Provide clear explanations for answers
- Vary difficulty levels

${customPrompt ? `Additional instructions: ${customPrompt}\n` : ''}

Content to process:
${content.substring(0, 8000)} ${content.length > 8000 ? '...[truncated]' : ''}

Return ONLY a valid JSON array with this exact structure:
[
  {
    "question_text": "Question here",
    "question_type": "multiple_choice|select_all|true_false|short_answer",
    "options": ["Option 1", "Option 2", "Option 3", "Option 4"] or null for short_answer and true_false,
    "correct_answers": ["Correct answer(s)"],
    "explanation": "Explanation of the correct answer",
    "difficulty_level": 1-5
  }
]

For true_false questions:
- Set "options": null
- Set "correct_answers": ["True"] or ["False"]

IMPORTANT:
- Ensure the response is valid JSON that can be parsed directly
- Complete ALL questions fully - do not truncate any content
- End the JSON array properly with a closing bracket ]
- Keep explanations concise but complete to avoid token limits`;

    return basePrompt;
  }

  private async callOpenRouter(prompt: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        {
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 8000, // Increased for longer quiz responses
          top_p: 0.9
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://chewyai.com',
            'X-Title': 'ChewyAI Study Material Generator'
          },
          timeout: 60000 // 60 second timeout
        }
      );

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid response from AI service');
      }

      return response.data.choices[0].message.content;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('AI service rate limit exceeded. Please try again later.');
        } else if (error.response?.status === 401) {
          throw new Error('AI service authentication failed');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('AI service request timed out');
        }
      }
      throw new Error(`AI service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseFlashcardResponse(response: string): Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged'>[] {
    try {
      // Clean response - remove any markdown formatting
      const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
      
      const flashcards = JSON.parse(cleanResponse);
      
      if (!Array.isArray(flashcards)) {
        throw new Error('Response is not an array');
      }

      return flashcards.map((card, index) => {
        if (!card.front || !card.back) {
          throw new Error(`Invalid flashcard at index ${index}: missing front or back`);
        }
        
        return {
          front: String(card.front).trim(),
          back: String(card.back).trim(),
          difficulty_level: this.validateDifficultyLevel(card.difficulty_level),
          is_ai_generated: true,
          times_reviewed: 0,
          last_reviewed_at: undefined
        };
      });
    } catch (error) {
      console.error('Failed to parse flashcard response:', response);
      throw new Error(`Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseQuizResponse(response: string): Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[] {
    try {
      // Clean response - remove any markdown formatting
      const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();

      // Check if response appears to be truncated
      let finalResponse = cleanResponse;
      if (!cleanResponse.endsWith(']') && !cleanResponse.endsWith('}]')) {
        console.warn('Response appears to be truncated, attempting to fix:', cleanResponse.slice(-200));

        // Try to salvage the response by finding the last complete question
        const lastCompleteQuestionIndex = cleanResponse.lastIndexOf('},');
        if (lastCompleteQuestionIndex > 0) {
          // Truncate to the last complete question and close the array
          finalResponse = cleanResponse.substring(0, lastCompleteQuestionIndex + 1) + '\n]';
          console.log('Attempting to salvage truncated response with', finalResponse.split('},').length, 'questions');
        } else {
          throw new Error('AI response appears to be truncated and cannot be salvaged. Try reducing the number of questions or content length.');
        }
      }

      const questions = JSON.parse(finalResponse);
      
      if (!Array.isArray(questions)) {
        throw new Error('Response is not an array');
      }

      return questions.map((question, index) => {
        if (!question.question_text || !question.question_type || !question.correct_answers) {
          throw new Error(`Invalid question at index ${index}: missing required fields`);
        }

        const validTypes = ['multiple_choice', 'select_all', 'true_false', 'short_answer'];
        if (!validTypes.includes(question.question_type)) {
          throw new Error(`Invalid question type at index ${index}: ${question.question_type}`);
        }

        return {
          question_text: String(question.question_text).trim(),
          question_type: question.question_type,
          options: question.options ? question.options.map((opt: any) => String(opt).trim()) : undefined,
          correct_answers: Array.isArray(question.correct_answers)
            ? question.correct_answers.map((ans: any) => String(ans).trim())
            : [String(question.correct_answers).trim()],
          explanation: question.explanation ? String(question.explanation).trim() : undefined,
          difficulty_level: this.validateDifficultyLevel(question.difficulty_level),
          is_ai_generated: true,
          times_attempted: 0,
          times_correct: 0
        };
      });
    } catch (error) {
      console.error('Failed to parse quiz response:', response);
      throw new Error(`Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private validateDifficultyLevel(level: any): number {
    const numLevel = Number(level);
    if (isNaN(numLevel) || numLevel < 1 || numLevel > 5) {
      return 3; // Default to medium difficulty
    }
    return Math.round(numLevel);
  }

  // Test AI service connectivity
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.callOpenRouter('Respond with "OK" if you can read this message.');
      return response.toLowerCase().includes('ok');
    } catch (error) {
      console.error('AI service connection test failed:', error);
      return false;
    }
  }
}

export class AIError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'AIError';
  }
}

export const aiService = new AIService();
