import{r as m,j as e,m as a,H as x,e as f,f as w,h as j,i as n,k as u,l as h,n as N}from"./index-ae0554d4.js";const v=[{id:"1",category:"getting-started",question:"How do I get started with ChewyAI?",answer:"Start by uploading your documents in the Documents section, then use our AI-powered tools to generate flashcards or quizzes from your content. You can also create study materials manually."},{id:"2",category:"documents",question:"What file formats are supported?",answer:"ChewyAI supports PDF, DOCX, TXT, and PPTX files. Make sure your documents contain readable text for the best AI generation results."},{id:"3",category:"flashcards",question:"How does AI flashcard generation work?",answer:"Our AI analyzes your uploaded documents and creates relevant flashcards with questions and answers. You can specify the number of cards to generate and provide custom prompts for better results."},{id:"4",category:"quizzes",question:"Can I create different types of quiz questions?",answer:"Yes! ChewyAI supports multiple choice, true/false, and short answer questions. You can specify the question types when generating quizzes with AI."},{id:"5",category:"billing",question:"How does the credit system work?",answer:"Credits are used for AI-powered features like generating flashcards and quizzes. Manual creation is always free. You can purchase credit packages or subscribe for unlimited usage."},{id:"6",category:"getting-started",question:"How do I study with my created materials?",answer:"Navigate to your study sets from the dashboard and choose your preferred study mode. Use keyboard shortcuts for efficient navigation during study sessions."}],o=[{id:"getting-started",label:"Getting Started",icon:N},{id:"documents",label:"Documents",icon:h},{id:"flashcards",label:"Flashcards",icon:u},{id:"quizzes",label:"Quizzes",icon:n},{id:"billing",label:"Billing & Credits",icon:x}],C=()=>{var l,c;const[s,p]=m.useState("getting-started"),[r,g]=m.useState(null),d=v.filter(t=>t.category===s),y=t=>{g(r===t?null:t)};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsx("div",{className:"text-center mb-12",children:e.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[e.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:"Help & Support"}),e.jsx("p",{className:"text-xl text-gray-400 max-w-2xl mx-auto",children:"Find answers to common questions and learn how to make the most of ChewyAI"})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h2",{className:"text-lg font-semibold text-white mb-4",children:"Categories"}),e.jsx("nav",{className:"space-y-2",children:o.map(t=>{const i=t.icon,b=s===t.id;return e.jsxs("button",{onClick:()=>p(t.id),className:`
                        w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left
                        transition-all duration-200
                        ${b?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(i,{className:"w-5 h-5"}),e.jsx("span",{className:"font-medium",children:t.label})]},t.id)})})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary mt-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-3",children:"Need More Help?"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Can't find what you're looking for? Contact our support team."}),e.jsxs("button",{className:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg transition-colors",children:[e.jsx(x,{className:"w-4 h-4"}),e.jsx("span",{children:"Contact Support"}),e.jsx(f,{className:"w-4 h-4"})]})]})]}),e.jsx("div",{className:"lg:col-span-3",children:e.jsxs(a.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"p-6 border-b border-border-primary",children:[e.jsxs("h2",{className:"text-2xl font-bold text-white",children:[(l=o.find(t=>t.id===s))==null?void 0:l.label," FAQ"]}),e.jsxs("p",{className:"text-gray-400 mt-2",children:["Frequently asked questions about ",(c=o.find(t=>t.id===s))==null?void 0:c.label.toLowerCase()]})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"space-y-4",children:d.map((t,i)=>e.jsxs(a.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:i*.1},className:"border border-border-primary rounded-lg overflow-hidden",children:[e.jsxs("button",{onClick:()=>y(t.id),className:"w-full flex items-center justify-between p-4 text-left hover:bg-background-tertiary transition-colors",children:[e.jsx("span",{className:"font-medium text-white pr-4",children:t.question}),r===t.id?e.jsx(w,{className:"w-5 h-5 text-gray-400 flex-shrink-0"}):e.jsx(j,{className:"w-5 h-5 text-gray-400 flex-shrink-0"})]}),e.jsx(a.div,{initial:!1,animate:{height:r===t.id?"auto":0,opacity:r===t.id?1:0},transition:{duration:.3},className:"overflow-hidden",children:e.jsx("div",{className:"p-4 pt-0 border-t border-border-primary",children:e.jsx("p",{className:"text-gray-300 leading-relaxed",children:t.answer})})})]},t.id))}),d.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(n,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-400 mb-2",children:"No FAQs Available"}),e.jsx("p",{className:"text-gray-500",children:"We're working on adding more helpful content for this category."})]})]})]},s)})]}),e.jsxs("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary text-center",children:[e.jsx(u,{className:"w-12 h-12 text-primary-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Create Flashcards"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Generate AI-powered flashcards from your documents"}),e.jsx("button",{className:"text-primary-400 hover:text-primary-300 font-medium",children:"Get Started →"})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary text-center",children:[e.jsx(n,{className:"w-12 h-12 text-primary-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Create Quizzes"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Build interactive quizzes with multiple question types"}),e.jsx("button",{className:"text-primary-400 hover:text-primary-300 font-medium",children:"Get Started →"})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary text-center",children:[e.jsx(h,{className:"w-12 h-12 text-primary-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Upload Documents"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Upload and manage your study documents"}),e.jsx("button",{className:"text-primary-400 hover:text-primary-300 font-medium",children:"Get Started →"})]})]})]})})};export{C as HelpPage};
